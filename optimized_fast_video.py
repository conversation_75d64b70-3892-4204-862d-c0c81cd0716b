#!/usr/bin/env python3
"""
Optimized Fast Video Transcription

Правильная оптимизация для быстрого отклика транскрипции:
- Оптимизированные чанки (200ms)
- Реальная скорость стриминга (1.0x)
- Минимальные задержки обработки
- Оптимизированные настройки Deepgram
"""

import asyncio
import subprocess
import threading
import time
import sys
import os
import tempfile
from pathlib import Path
from dotenv import load_dotenv

# Load environment
load_dotenv()
sys.path.append('.')
sys.path.append('src')

try:
    from deepgram_live_demo import DeepgramOfficialTest
    from realtime_audio_streamer import RealTimeAudioStreamer
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


class OptimizedVideoPlayer:
    """Optimized video player"""
    
    def __init__(self, video_path: str):
        self.video_path = video_path
        self.player_process = None
        self.is_playing = False
        self.start_time = None
        
        print(f"⚡ Optimized video player: {Path(video_path).name}")
    
    def play_video_optimized(self):
        """Play video with optimized settings"""
        cmd = [
            'ffplay',
            '-autoexit',
            '-loglevel', 'quiet',
            '-fast',  # Fast decoding
            self.video_path
        ]
        
        print(f"⚡ Starting optimized video playback...")
        
        try:
            self.is_playing = True
            self.start_time = time.time()
            
            self.player_process = subprocess.Popen(
                cmd,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            
            self.player_process.wait()
            
            duration = time.time() - self.start_time
            print(f"⚡ Video completed in {duration:.1f}s")
            self.is_playing = False
            
            return True
            
        except Exception as e:
            print(f"❌ Video error: {e}")
            self.is_playing = False
            return False
    
    def get_elapsed_time(self):
        """Get elapsed time"""
        if self.start_time and self.is_playing:
            return time.time() - self.start_time
        return 0
    
    def stop_video(self):
        """Stop video"""
        if self.player_process and self.is_playing:
            try:
                self.player_process.terminate()
                self.player_process.wait(timeout=1)
            except:
                try:
                    self.player_process.kill()
                except:
                    pass
            self.is_playing = False


class OptimizedAudioExtractor:
    """Optimized audio extraction"""
    
    def __init__(self, video_path: str):
        self.video_path = video_path
        self.temp_audio_file = None
        
        print("⚡ Optimized audio extractor initialized")
    
    def extract_audio_optimized(self):
        """Extract audio with optimization"""
        try:
            temp_dir = tempfile.gettempdir()
            self.temp_audio_file = os.path.join(temp_dir, f"optimized_{int(time.time())}.wav")
            
            print("⚡ Optimized audio extraction...")
            
            cmd = [
                'ffmpeg',
                '-i', self.video_path,
                '-vn',  # No video
                '-acodec', 'pcm_s16le',  # PCM 16-bit
                '-ar', '16000',  # 16kHz
                '-ac', '1',  # Mono
                '-threads', '0',  # All CPU cores
                self.temp_audio_file,
                '-y',
                '-loglevel', 'quiet'
            ]
            
            start_time = time.time()
            subprocess.run(cmd, check=True)
            extraction_time = time.time() - start_time
            
            if os.path.exists(self.temp_audio_file):
                file_size = os.path.getsize(self.temp_audio_file)
                print(f"⚡ Extraction: {extraction_time:.2f}s, {file_size} bytes")
                return True
            else:
                return False
            
        except Exception as e:
            print(f"❌ Extraction error: {e}")
            return False
    
    def cleanup(self):
        """Cleanup"""
        if self.temp_audio_file and os.path.exists(self.temp_audio_file):
            try:
                os.remove(self.temp_audio_file)
                print(f"🗑️ Cleaned up: {self.temp_audio_file}")
            except:
                pass


class OptimizedTranscriber:
    """Optimized transcriber for maximum responsiveness"""
    
    def __init__(self, deepgram_api_key: str, gemini_api_key: str, audio_file: str):
        self.audio_file = audio_file
        self.deepgram_api_key = deepgram_api_key
        self.gemini_api_key = gemini_api_key
        
        # Initialize transcriber
        self.transcriber = DeepgramOfficialTest(
            deepgram_api_key,
            gemini_api_key,
            audio_file
        )
        
        print("⚡ Optimized transcriber initialized")
    
    async def run_optimized_transcription(self, video_player: OptimizedVideoPlayer):
        """Run optimized transcription with maximum responsiveness"""
        try:
            print("⚡ Setting up optimized transcription...")
            
            # Setup connection
            if not self.transcriber.setup_connection():
                print("❌ Failed to setup connection")
                return False
            
            print("✅ Connection established")
            
            # Minimal wait (reduced from 2s to 0.5s)
            await asyncio.sleep(0.5)
            
            # Create OPTIMIZED streamer
            streamer = RealTimeAudioStreamer(
                chunk_duration_ms=200,   # 200ms chunks (optimized responsiveness)
                speed_multiplier=1.0    # REAL-TIME speed (correct!)
            )

            print("⚡ OPTIMIZED streaming settings:")
            print("   • Chunk size: 200ms (optimized responsiveness)")
            print("   • Speed: 1.0x (REAL-TIME - correct!)")
            print("   • Minimal processing delays")
            print("   • Optimized transcription responsiveness")
            
            # Load audio
            if not streamer.load_audio_file(self.audio_file):
                print("❌ Failed to load audio")
                return False
            
            # Optimized callback with minimal overhead
            chunk_count = 0
            last_progress_time = 0
            
            def on_optimized_chunk(chunk_bytes: bytes, start_time: float, end_time: float):
                nonlocal chunk_count, last_progress_time
                if self.transcriber.dg_connection:
                    try:
                        self.transcriber.dg_connection.send(chunk_bytes)
                        chunk_count += 1
                        
                        # Show progress every 1 second (reduced frequency)
                        current_time = time.time()
                        if current_time - last_progress_time >= 1.0:
                            video_time = video_player.get_elapsed_time()
                            print(f"\r⚡ OPTIMIZED: Video {video_time:.1f}s | Audio {end_time:.1f}s | {chunk_count} chunks", end='', flush=True)
                            last_progress_time = current_time
                    except Exception:
                        pass
            
            # Set callback
            streamer.on_audio_chunk = on_optimized_chunk
            
            print("🚀 Starting OPTIMIZED real-time streaming...")
            
            # Start optimized streaming
            success = await streamer.start_streaming()
            
            if success:
                print(f"\n⚡ OPTIMIZED streaming completed: {chunk_count} chunks")
            else:
                print(f"\n⚠️ OPTIMIZED streaming issues: {chunk_count} chunks")
            
            return success
            
        except Exception as e:
            print(f"❌ Optimized transcription error: {e}")
            return False
    
    def cleanup(self):
        """Cleanup"""
        if self.transcriber:
            self.transcriber.cleanup()


async def run_optimized_fast_video_transcription(video_path: str):
    """Run optimized fast video transcription"""
    
    print("⚡ Daily Catalyst - OPTIMIZED Fast Video Transcription")
    print("=" * 70)
    print("⚡ Optimized responsiveness")
    print("🎯 200ms chunks (optimized responsive)")
    print("⏰ 1.0x real-time speed (CORRECT!)")
    print("🚀 Minimal processing delays")
    print("⚡ Optimized transcription responsiveness")
    print("=" * 70)
    
    # Check API keys
    deepgram_key = os.getenv('DEEPGRAM_API_KEY')
    gemini_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
    
    if not deepgram_key or not gemini_key:
        print("❌ Missing API keys")
        return False
    
    print(f"✅ API keys ready")
    
    # Initialize optimized components
    video_player = OptimizedVideoPlayer(video_path)
    audio_extractor = OptimizedAudioExtractor(video_path)
    
    try:
        # Optimized audio extraction
        print("⚡ Step 1: Optimized audio extraction...")
        if not audio_extractor.extract_audio_optimized():
            return False
        
        # Initialize optimized transcriber
        transcriber = OptimizedTranscriber(
            deepgram_key, 
            gemini_key, 
            audio_extractor.temp_audio_file
        )
        
        print("⚡ Step 2: Start optimized video and transcription...")
        
        # Start video
        video_thread = threading.Thread(
            target=video_player.play_video_optimized,
            daemon=True
        )
        video_thread.start()
        
        # Minimal delay (reduced from 1s to 0.3s)
        await asyncio.sleep(0.3)
        
        # Start optimized transcription
        success = await transcriber.run_optimized_transcription(video_player)
        
        # Wait for video
        video_thread.join()
        
        # Minimal wait for final transcripts (reduced from 5s to 2s)
        print("\n⚡ Optimized final processing...")
        await asyncio.sleep(2)
        
        return success
        
    except KeyboardInterrupt:
        print("\n⏹️ Stopped by user")
        return True
    except Exception as e:
        print(f"❌ Optimized error: {e}")
        return False
    finally:
        # Cleanup
        video_player.stop_video()
        if 'transcriber' in locals():
            transcriber.cleanup()
        audio_extractor.cleanup()


def main():
    """Main function"""
    
    import argparse
    parser = argparse.ArgumentParser(description="Optimized Fast Video Transcription")
    parser.add_argument("video", help="Path to video file")
    
    args = parser.parse_args()
    
    video_path = Path(args.video)
    if not video_path.exists():
        print(f"❌ Video file not found: {video_path}")
        return 1
    
    try:
        success = asyncio.run(run_optimized_fast_video_transcription(str(video_path)))
        
        if success:
            print("\n⚡ OPTIMIZED fast video transcription completed!")
            print("🎯 200ms chunks for optimized responsiveness")
            print("⏰ Real-time speed (1.0x) - CORRECT!")
            print("⚡ Minimal delays for optimized response")
        else:
            print("\n⚠️ Optimized transcription had issues")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⏹️ Stopped by user")
        return 1
    except Exception as e:
        print(f"\n❌ Error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
