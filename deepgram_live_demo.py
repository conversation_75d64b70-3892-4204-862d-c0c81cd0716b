#!/usr/bin/env python3
"""
Test script using official Deepgram SDK
Based on flask-live-transcription starter
"""

import asyncio
import logging
import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv
from typing import List, Dict, Optional
from dataclasses import dataclass

# Load environment variables
load_dotenv()

# Install deepgram-sdk if not available
try:
    from deepgram import (
        DeepgramClient,
        LiveTranscriptionEvents,
        LiveOptions,
        DeepgramClientOptions
    )
except ImportError:
    print("❌ Deepgram SDK not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "deepgram-sdk"])
    from deepgram import (
        DeepgramClient,
        LiveTranscriptionEvents,
        LiveOptions,
        DeepgramClientOptions
    )

# Install google-generativeai if not available
try:
    import google.generativeai as genai
except ImportError:
    print("❌ Google Generative AI not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "google-generativeai"])
    import google.generativeai as genai

# Add src to path for our audio streamer
sys.path.append('src')

# Configure logging - disable Deepgram SDK logs
logging.basicConfig(level=logging.WARNING)
logging.getLogger("deepgram").setLevel(logging.CRITICAL)
logging.getLogger("realtime_audio_streamer").setLevel(logging.CRITICAL)
logger = logging.getLogger(__name__)


@dataclass
class TranscriptSegment:
    """Represents a single transcript segment with metadata"""
    timestamp: float
    speaker: str
    text: str
    is_final: bool = False
    off_topic_analysis: Optional[str] = None


class RealTimeOffTopicAnalyzer:
    """Real-time off-topic analyzer using Gemini API with configurable prompts and settings"""

    def __init__(self, gemini_api_key: str, config_loader=None):
        """Initialize the real-time analyzer with configurable prompts and model settings"""
        self.api_key = gemini_api_key
        self.transcript_history: List[TranscriptSegment] = []
        self.analysis_cache: Dict[str, str] = {}
        self.ai_analysis_cache: Dict[str, str] = {}  # Separate cache for AI results
        self.pending_ai_analysis: Dict[str, bool] = {}  # Track pending AI analysis

        # Load configuration
        if config_loader is None:
            # Import here to avoid circular imports
            from config.config_loader import get_config_loader
            config_loader = get_config_loader()

        self.config_loader = config_loader
        self.config = config_loader.load_all_configs()

        if not self.config.is_valid:
            print("⚠️ Configuration errors detected:")
            for error in self.config.errors:
                print(f"   - {error}")
            print("⚠️ Using fallback settings...")

        # Load model configuration from settings
        models_config = self.config_loader.get_setting('api.gemini.models', [])
        if not models_config:
            # Fallback to hardcoded models if config not available
            models_config = [
                {
                    'name': 'gemini-2.5-flash',
                    'display_name': 'Gemini 2.5 Flash',
                    'rpm_limit': 8,
                    'daily_limit': 200
                },
                {
                    'name': 'gemini-2.0-flash',
                    'display_name': 'Gemini 2.0 Flash',
                    'rpm_limit': 12,
                    'daily_limit': 150
                },
                {
                    'name': 'gemma-3n-e2b-it',
                    'display_name': 'Gemma 3n-e2b',
                    'rpm_limit': 25,
                    'daily_limit': 1000
                }
            ]

        # Initialize model fallback system
        self.available_models = []
        for model_config in models_config:
            model = {
                'name': model_config['name'],
                'display_name': model_config['display_name'],
                'rpm_limit': model_config['rpm_limit'],
                'daily_limit': model_config['daily_limit'],
                'requests': [],
                'daily_requests': 0,
                'blocked_until': None
            }
            self.available_models.append(model)

        self.current_model_index = 0

        # Configure Gemini
        genai.configure(api_key=self.api_key)
        self._initialize_current_model()

        print(f"🤖 Real-time Off-Topic Analyzer initialized (Multi-Model Fallback)")
        print(f"🎯 Primary: {self.current_model['display_name']}")
        print(f"🔄 Fallback: {len(self.available_models)-1} backup models available")

    def _initialize_current_model(self):
        """Initialize the current model"""
        self.current_model = self.available_models[self.current_model_index]
        self.model = genai.GenerativeModel(self.current_model['name'])

    def _can_make_ai_request(self) -> bool:
        """Check if current model can handle request, fallback if needed"""
        import time
        now = time.time()

        # Check current model availability
        current = self.current_model

        # Remove old requests (older than 1 minute)
        current['requests'] = [req_time for req_time in current['requests'] if now - req_time < 60]

        # Check if current model is available
        minute_ok = len(current['requests']) < current['rpm_limit']
        daily_ok = current['daily_requests'] < current['daily_limit']

        if minute_ok and daily_ok:
            return True

        # Current model exhausted, try fallback
        return self._try_fallback_model()

    def _try_fallback_model(self) -> bool:
        """Try to switch to next available model"""
        import time
        now = time.time()

        original_index = self.current_model_index

        # Try each model in sequence
        for i in range(len(self.available_models)):
            next_index = (self.current_model_index + 1) % len(self.available_models)

            if next_index == original_index:
                break  # We've tried all models

            self.current_model_index = next_index
            candidate = self.available_models[next_index]

            # Clean old requests
            candidate['requests'] = [req_time for req_time in candidate['requests'] if now - req_time < 60]

            # Check availability
            minute_ok = len(candidate['requests']) < candidate['rpm_limit']
            daily_ok = candidate['daily_requests'] < candidate['daily_limit']

            if minute_ok and daily_ok:
                # Switch to this model
                self._initialize_current_model()
                print(f"🔄 Switched to {self.current_model['display_name']} (quota available)")
                return True

        # No models available
        print(f"⚠️ All models exhausted - using local fallback")
        return False

    def _record_ai_request(self):
        """Record AI request for current model"""
        import time
        current = self.current_model
        current['requests'].append(time.time())
        current['daily_requests'] += 1

    def add_transcript_segment(self, segment: TranscriptSegment):
        """Add new transcript segment to history"""
        self.transcript_history.append(segment)

        # Keep ALL segments from the beginning of the meeting
        # No truncation - we need full context for accurate analysis



    def _should_use_ai_analysis(self, segment: TranscriptSegment) -> bool:
        """Ultra-selective AI analysis - only for highly suspicious segments"""
        text = segment.text.lower()

        # Get thresholds from configuration
        min_length = self.config_loader.get_setting('analysis.text_analysis.min_segment_length', 30)
        short_threshold = self.config_loader.get_setting('analysis.text_analysis.short_segment_threshold', 80)
        suspicious_threshold = self.config_loader.get_setting('analysis.text_analysis.suspicious_length_threshold', 120)

        # Skip short segments
        if len(segment.text) < min_length:
            return False

        # Get keyword lists from configuration
        normal_work_phrases = self.config_loader.get_setting(
            'fallback_prompts.local_analysis_keywords.normal_work_phrases',
            ['yesterday', 'today', 'working on', 'completed', 'finished',
             'will do', 'planning to', 'blocked by', 'waiting for',
             'testing', 'reviewing', 'meeting', 'sync', 'call']
        )

        if any(phrase in text for phrase in normal_work_phrases) and len(segment.text) < short_threshold:
            return False

        # Get suspicious indicators from configuration
        suspicious_indicators = self.config_loader.get_setting(
            'fallback_prompts.local_analysis_keywords.suspicious_indicators',
            ['flexbox', 'css', 'width of 0', 'missing prop', 'stack trace',
             'implementation', 'algorithm', 'debugging', 'error message',
             'how should', 'what do you think', 'maybe we should', 'perhaps',
             'should we use', 'what about', 'how about',
             'meeting efficiency', 'process', 'workflow', 'improve our',
             'we need to', 'we should change',
             'because', 'the reason was', 'what happened', 'i spent',
             'i had to', 'it took so long']
        )

        has_suspicious_content = any(indicator in text for indicator in suspicious_indicators)
        is_very_long = len(segment.text) > suspicious_threshold

        # Only use AI for clearly suspicious or very long segments
        return has_suspicious_content or is_very_long

    def analyze_new_phrase_sync(self, new_segment: TranscriptSegment) -> str:
        """Analyze new phrase in context of ENTIRE meeting history (synchronous version)"""
        try:
            # Build FULL context from ALL previous segments (from meeting start)
            context_lines = []
            for seg in self.transcript_history:  # ALL segments, not just last 10
                context_lines.append(f"[{seg.timestamp:.1f}s] {seg.speaker}: {seg.text}")

            full_context = "\n".join(context_lines) if context_lines else "Meeting start"

            # Format new phrase
            new_phrase = f"[{new_segment.timestamp:.1f}s] {new_segment.speaker}: {new_segment.text}"

            # Check cache first (but include full context length in cache key)
            cache_key = f"{len(self.transcript_history)}_{new_segment.text[:50]}_{len(full_context)}"
            if cache_key in self.analysis_cache:
                return self.analysis_cache[cache_key]

            # Enhanced analysis using full meeting context
            text_lower = new_segment.text.lower()
            text_length = len(new_segment.text)

            # Analyze meeting context to understand current phase
            meeting_duration = len(self.transcript_history)
            is_early_meeting = meeting_duration <= 3  # First few statements

            # Check if this is a continuation of previous off-topic discussion
            recent_context = ""
            if len(self.transcript_history) >= 2:
                recent_context = " ".join([seg.text.lower() for seg in self.transcript_history[-2:]])

            # MINIMAL LOCAL ANALYSIS - only as fallback when AI is unavailable
            # Primary analysis should be done by AI for accuracy

            # Get fallback keywords from configuration
            extremely_obvious_technical = self.config_loader.get_setting(
                'fallback_prompts.local_analysis_keywords.extremely_obvious_technical',
                ['width of 0 because of a missing prop',
                 'flexbox was inside another flexbox which had',
                 'stack trace shows', 'error message says',
                 'sql query returned', 'database connection failed']
            )

            extremely_obvious_meta = self.config_loader.get_setting(
                'fallback_prompts.local_analysis_keywords.extremely_obvious_meta',
                ['we need to improve our meeting efficiency',
                 'this meeting is taking too long',
                 'we should change our workflow']
            )

            # Get thresholds from configuration
            technical_threshold = self.config_loader.get_setting('analysis.local_analysis.technical_threshold', 1)
            meta_threshold = self.config_loader.get_setting('analysis.local_analysis.meta_threshold', 1)
            length_threshold = self.config_loader.get_setting('analysis.local_analysis.length_threshold', 80)
            very_long_threshold = self.config_loader.get_setting('analysis.local_analysis.very_long_threshold', 300)

            # Only flag if multiple extremely obvious indicators or very long technical explanations
            obvious_technical = sum(1 for phrase in extremely_obvious_technical if phrase in text_lower)
            obvious_meta = sum(1 for phrase in extremely_obvious_meta if phrase in text_lower)

            # VERY HIGH THRESHOLDS - only for extreme cases when AI is unavailable
            if obvious_technical >= technical_threshold and text_length > length_threshold:
                return "OFF_TOPIC: Excessive technical detail (fallback analysis)"
            elif obvious_meta >= meta_threshold:
                return "OFF_TOPIC: Meta-discussion about process (fallback analysis)"
            elif text_length > very_long_threshold:  # Extremely long segments
                return "OFF_TOPIC: Too verbose for standup format (fallback analysis)"

            # Cache result
            self.analysis_cache[cache_key] = "ON_TOPIC"
            return "ON_TOPIC"

        except Exception as e:
            logger.error(f"Error in real-time analysis: {e}")
            return "ON_TOPIC"  # Default to on-topic if analysis fails

    async def analyze_new_phrase(self, new_segment: TranscriptSegment) -> str:
        """Analyze new phrase in context of ENTIRE meeting history (async version)"""
        try:
            # Build FULL context from ALL previous segments (from meeting start)
            context_lines = []
            for seg in self.transcript_history:  # ALL segments, not just last 10
                context_lines.append(f"[{seg.timestamp:.1f}s] {seg.speaker}: {seg.text}")

            full_context = "\n".join(context_lines) if context_lines else "Meeting start"

            # Format new phrase
            new_phrase = f"[{new_segment.timestamp:.1f}s] {new_segment.speaker}: {new_segment.text}"

            # Check cache first (include full context in cache key)
            cache_key = f"{len(self.transcript_history)}_{new_segment.text[:50]}_{len(full_context)}"
            if cache_key in self.analysis_cache:
                return self.analysis_cache[cache_key]

            # Get prompt from configuration
            try:
                prompt_template = self.config_loader.get_prompt()
            except Exception as e:
                logger.warning(f"Failed to load prompt from config: {e}, using fallback")
                # Fallback prompt if configuration fails
                prompt_template = """
You are a strict Scrum Master analyzing a daily standup. Flag technical deep-dives as OFF-TOPIC.

MEETING CONTEXT:
{context}

NEW STATEMENT:
{new_phrase}

Answer ONLY:
- "OFF_TOPIC: [reason]" for violations
- "ON_TOPIC" for proper standup updates
"""

            # Prepare prompt with FULL meeting context
            prompt = prompt_template.format(
                context=full_context,
                new_phrase=new_phrase
            )

            # Generate analysis
            response = self.model.generate_content(prompt)

            if response.text:
                result = response.text.strip()
                self.analysis_cache[cache_key] = result
                return result
            else:
                return "ON_TOPIC"

        except Exception as e:
            logger.error(f"Error in real-time analysis: {e}")
            return "ON_TOPIC"  # Default to on-topic if analysis fails

    async def analyze_hybrid(self, new_segment: TranscriptSegment, callback=None) -> str:
        """
        Hybrid analysis: Fast local analysis + AI refinement in background

        Args:
            new_segment: The transcript segment to analyze
            callback: Optional callback function to handle AI analysis results

        Returns:
            Quick local analysis result (AI result sent via callback)
        """
        try:
            # 1. FAST LOCAL ANALYSIS (immediate response)
            quick_result = self.analyze_new_phrase_sync(new_segment)

            # 2. TRIGGER AI ANALYSIS IN BACKGROUND (if not already cached)
            segment_key = f"{new_segment.timestamp}_{new_segment.text[:50]}"

            if segment_key not in self.ai_analysis_cache and segment_key not in self.pending_ai_analysis:
                self.pending_ai_analysis[segment_key] = True

                # Start AI analysis in background
                asyncio.create_task(self._background_ai_analysis(new_segment, segment_key, callback))

            return quick_result

        except Exception as e:
            logger.error(f"Error in hybrid analysis: {e}")
            return "ON_TOPIC"

    async def _background_ai_analysis(self, segment: TranscriptSegment, segment_key: str, callback=None):
        """Background AI analysis with callback for result updates"""
        try:
            # Perform AI analysis
            ai_result = await self.analyze_new_phrase(segment)

            # Cache the result
            self.ai_analysis_cache[segment_key] = ai_result
            self.pending_ai_analysis.pop(segment_key, None)

            # If callback provided and result differs from quick analysis, notify
            if callback:
                quick_result = self.analyze_new_phrase_sync(segment)
                if ai_result != quick_result:
                    await callback(segment, quick_result, ai_result)

        except Exception as e:
            logger.error(f"Error in background AI analysis: {e}")
            self.pending_ai_analysis.pop(segment_key, None)

class DeepgramOfficialTest:
    """Test class using official Deepgram SDK with real-time off-topic analysis"""

    def __init__(self, deepgram_api_key: str, gemini_api_key: str, audio_file: str, config_loader=None):
        self.deepgram_api_key = deepgram_api_key
        self.gemini_api_key = gemini_api_key
        self.audio_file = audio_file

        # Load configuration
        if config_loader is None:
            from config.config_loader import get_config_loader
            config_loader = get_config_loader()

        self.config_loader = config_loader
        self.config = config_loader.load_all_configs()

        # Statistics
        self.transcript_count = 0
        self.final_count = 0
        self.interim_count = 0
        self.off_topic_count = 0
        self.start_time = None
        self.chunks_sent = 0
        self.last_chunk_time = 0
        self.last_progress = 0

        # Deepgram client configuration from settings
        verbose_level = self.config_loader.get_setting('api.deepgram.verbose_logging', False)
        keepalive = self.config_loader.get_setting('api.deepgram.keepalive', True)

        config = DeepgramClientOptions(
            verbose=logging.CRITICAL if not verbose_level else logging.INFO,
            options={"keepalive": "true" if keepalive else "false"}
        )
        self.deepgram = DeepgramClient(deepgram_api_key, config)
        self.dg_connection = None

        # Real-time off-topic analyzer with configuration
        self.off_topic_analyzer = RealTimeOffTopicAnalyzer(gemini_api_key, config_loader)

        print("🎙️  Live Transcription with Real-Time Off-Topic Analysis")
        print("=" * 65)
        print("🤖 Powered by Deepgram + Google Gemini 2.5 Flash (Enhanced)")
        print("� Higher quotas: 25/min, 100/day - more AI analysis!")
        print("🎯 Optimized for efficient standup meeting analysis")
        print("=" * 65)
    
    def setup_connection(self):
        """Setup Deepgram connection with callbacks"""
        # Initialize connection (use websocket instead of live)
        self.dg_connection = self.deepgram.listen.websocket.v("1")

        # Setup event handlers with lambda to handle SDK callback signature
        self.dg_connection.on(LiveTranscriptionEvents.Open, lambda self_ref, *args, **kwargs: self.on_open(*args, **kwargs))
        self.dg_connection.on(LiveTranscriptionEvents.Transcript, lambda self_ref, result, **kwargs: self.on_message(result, **kwargs))
        self.dg_connection.on(LiveTranscriptionEvents.Close, lambda self_ref, *args, **kwargs: self.on_close(*args, **kwargs))
        self.dg_connection.on(LiveTranscriptionEvents.Error, lambda self_ref, error, **kwargs: self.on_error(error, **kwargs))

        # Define options with configuration
        options = LiveOptions(
            model=self.config_loader.get_setting('api.deepgram.model', 'nova-3'),
            language=self.config_loader.get_setting('api.deepgram.language', 'en-US'),
            smart_format=self.config_loader.get_setting('api.deepgram.smart_format', True),
            diarize=self.config_loader.get_setting('api.deepgram.diarize', True),
            interim_results=self.config_loader.get_setting('api.deepgram.interim_results', False),
            punctuate=self.config_loader.get_setting('api.deepgram.punctuate', True),
            encoding=self.config_loader.get_setting('api.deepgram.encoding', 'linear16'),
            sample_rate=self.config_loader.get_setting('api.deepgram.sample_rate', 16000),
            channels=self.config_loader.get_setting('api.deepgram.channels', 1),
            filler_words=self.config_loader.get_setting('api.deepgram.filler_words', False),
            numerals=self.config_loader.get_setting('api.deepgram.numerals', True)
        )

        # Start connection
        if self.dg_connection.start(options) is False:
            return False

        return True
    
    def on_open(self, *args, **kwargs):
        """Handle connection open"""
        self.start_time = time.time()
        print("🎙️  Live transcription started...")
        print("=" * 50)

    def on_message(self, result, **kwargs):
        """Handle transcription results with real-time off-topic analysis"""
        if not result:
            return

        try:
            # Get transcript
            transcript = None
            if hasattr(result, 'channel') and hasattr(result.channel, 'alternatives'):
                if result.channel.alternatives and len(result.channel.alternatives) > 0:
                    transcript = result.channel.alternatives[0].transcript

            if transcript and len(transcript.strip()) > 0:
                self.transcript_count += 1

                # Determine result type
                is_final = getattr(result, 'is_final', False)
                speech_final = getattr(result, 'speech_final', False)

                # Only process final results for analysis
                if speech_final or (is_final and len(transcript.strip()) > 3):
                    self.final_count += 1

                    # Get speaker information with enhanced detection
                    speakers = set()
                    words = getattr(result.channel.alternatives[0], 'words', [])

                    # Collect all speakers from words
                    for word in words:
                        if hasattr(word, 'speaker') and word.speaker is not None:
                            speakers.add(word.speaker)

                    # If no speaker info in words, try alternative methods
                    if not speakers:
                        # Check if there's speaker info at alternative level
                        alt = result.channel.alternatives[0]
                        if hasattr(alt, 'speaker') and alt.speaker is not None:
                            speakers.add(alt.speaker)

                        # Check metadata for speaker info
                        if hasattr(result, 'metadata') and hasattr(result.metadata, 'speaker'):
                            speakers.add(result.metadata.speaker)

                    # Format speaker info
                    if speakers:
                        speaker_list = sorted(speakers)
                        if len(speaker_list) == 1:
                            speaker_info = f"Speaker {speaker_list[0]}"
                        else:
                            speaker_info = f"Speakers {', '.join(map(str, speaker_list))}"
                    else:
                        # Fallback: try to detect speaker change based on timing
                        speaker_info = "Unknown Speaker"

                    # Get timing info - try multiple sources
                    start_time = 0

                    # Method 1: From result metadata
                    if hasattr(result, 'metadata') and hasattr(result.metadata, 'request_id'):
                        start_time = getattr(result.metadata, 'start', 0)

                    # Method 2: From channel alternatives
                    if start_time == 0 and hasattr(result, 'channel') and hasattr(result.channel, 'alternatives'):
                        if result.channel.alternatives:
                            start_time = getattr(result.channel.alternatives[0], 'start', 0)

                    # Method 3: From words timing
                    if start_time == 0 and words:
                        first_word = words[0]
                        if hasattr(first_word, 'start'):
                            start_time = first_word.start

                    # Method 4: Calculate from stream time
                    if start_time == 0 and self.start_time:
                        start_time = time.time() - self.start_time

                    # Create transcript segment
                    segment = TranscriptSegment(
                        timestamp=start_time,
                        speaker=speaker_info,
                        text=transcript,
                        is_final=True
                    )

                    # Perform real-time off-topic analysis (hybrid version)
                    # Use thread-safe approach for hybrid analysis
                    self._analyze_and_display_segment_hybrid_sync(segment)
                else:
                    self.interim_count += 1

        except Exception as e:
            # Silent error handling
            logger.error(f"Error in on_message: {e}")

    def _analyze_and_display_segment_sync(self, segment: TranscriptSegment):
        """Analyze segment and display with off-topic status (synchronous version)"""
        try:
            # Add to analyzer history
            self.off_topic_analyzer.add_transcript_segment(segment)

            # Perform real-time analysis (synchronous)
            analysis_result = self.off_topic_analyzer.analyze_new_phrase_sync(segment)

            # Format output based on analysis
            timestamp = f"[{segment.timestamp:.1f}s]"

            if analysis_result.startswith("OFF_TOPIC"):
                self.off_topic_count += 1
                # Extract reason from analysis
                reason = analysis_result.replace("OFF_TOPIC:", "").strip()
                print(f"{timestamp} 🚨 {segment.speaker}: {segment.text}")
                print(f"         ⚠️  OFF-TOPIC: {reason}")
            else:
                print(f"{timestamp} ✅ {segment.speaker}: {segment.text}")

        except Exception as e:
            # Fallback to normal display if analysis fails
            timestamp = f"[{segment.timestamp:.1f}s]"
            print(f"{timestamp} {segment.speaker}: {segment.text}")
            logger.error(f"Error in analysis: {e}")

    async def _analyze_and_display_segment_hybrid(self, segment: TranscriptSegment):
        """Hybrid analysis: Fast display + AI refinement in background"""
        try:
            # Add to analyzer history
            self.off_topic_analyzer.add_transcript_segment(segment)

            # Define callback for AI analysis updates
            async def ai_analysis_callback(seg, quick_result, ai_result):
                """Handle AI analysis results that differ from quick analysis"""
                if quick_result != ai_result:
                    timestamp = f"[{seg.timestamp:.1f}s]"
                    if ai_result.startswith("OFF_TOPIC") and not quick_result.startswith("OFF_TOPIC"):
                        # AI found off-topic that quick analysis missed
                        reason = ai_result.replace("OFF_TOPIC:", "").strip()
                        print(f"{timestamp} 🔄 AI UPDATE: {seg.speaker}: {seg.text}")
                        print(f"         🤖 AI DETECTED OFF-TOPIC: {reason}")
                        self.off_topic_count += 1
                    elif not ai_result.startswith("OFF_TOPIC") and quick_result.startswith("OFF_TOPIC"):
                        # AI corrected false positive from quick analysis
                        print(f"{timestamp} 🔄 AI CORRECTION: {seg.speaker}: {seg.text}")
                        print(f"         ✅ AI CONFIRMED ON-TOPIC (was flagged by quick analysis)")
                        self.off_topic_count = max(0, self.off_topic_count - 1)

            # Perform hybrid analysis (quick result + background AI)
            analysis_result = await self.off_topic_analyzer.analyze_hybrid(segment, ai_analysis_callback)

            # Display immediate result based on quick analysis
            timestamp = f"[{segment.timestamp:.1f}s]"

            if analysis_result.startswith("OFF_TOPIC"):
                self.off_topic_count += 1
                reason = analysis_result.replace("OFF_TOPIC:", "").strip()
                print(f"{timestamp} 🚨 {segment.speaker}: {segment.text}")
                print(f"         ⚠️  OFF-TOPIC: {reason} (quick analysis)")
            else:
                print(f"{timestamp} ✅ {segment.speaker}: {segment.text}")

        except Exception as e:
            # Fallback to normal display if analysis fails
            timestamp = f"[{segment.timestamp:.1f}s]"
            print(f"{timestamp} {segment.speaker}: {segment.text}")
            logger.error(f"Error in hybrid analysis: {e}")

    def _analyze_and_display_segment_hybrid_sync(self, segment: TranscriptSegment):
        """AI-first analysis with local fallback (sync version for Deepgram callbacks)"""
        try:
            # Add to analyzer history
            self.off_topic_analyzer.add_transcript_segment(segment)

            timestamp = f"[{segment.timestamp:.1f}s]"
            segment_key = f"{segment.timestamp}_{segment.text[:50]}"

            # Check if we should use AI analysis (most segments)
            should_use_ai = (
                self.off_topic_analyzer._should_use_ai_analysis(segment) and
                self.off_topic_analyzer._can_make_ai_request() and
                segment_key not in self.off_topic_analyzer.ai_analysis_cache and
                segment_key not in self.off_topic_analyzer.pending_ai_analysis
            )

            if should_use_ai:
                # AI-FIRST APPROACH: Show pending, then update with AI result
                current_model_name = self.off_topic_analyzer.current_model['display_name']
                print(f"{timestamp} 🔄 {segment.speaker}: {segment.text}")
                print(f"         ⏳ {current_model_name} analyzing...")

                self.off_topic_analyzer.pending_ai_analysis[segment_key] = True
                self.off_topic_analyzer._record_ai_request()

                # Start AI analysis in background thread
                import threading
                threading.Thread(
                    target=self._background_ai_analysis_sync_primary,
                    args=(segment, segment_key, timestamp),
                    daemon=True
                ).start()

            elif not self.off_topic_analyzer._can_make_ai_request():
                # All models exhausted - use local fallback
                local_result = self.off_topic_analyzer.analyze_new_phrase_sync(segment)

                current_model = self.off_topic_analyzer.current_model
                quota_status = f"{current_model['display_name']}: {current_model['daily_requests']}/{current_model['daily_limit']} daily"

                if local_result.startswith("OFF_TOPIC"):
                    self.off_topic_count += 1
                    reason = local_result.replace("OFF_TOPIC:", "").strip()
                    print(f"{timestamp} 🚨 {segment.speaker}: {segment.text}")
                    print(f"         ⚠️  {reason}")
                    print(f"         💰 All models exhausted ({quota_status}) - using local fallback")
                else:
                    print(f"{timestamp} ✅ {segment.speaker}: {segment.text}")
                    print(f"         💰 All models exhausted ({quota_status}) - using local fallback")

            else:
                # Normal segment - skip AI analysis to save quota
                print(f"{timestamp} ✅ {segment.speaker}: {segment.text}")

        except Exception as e:
            # Fallback to normal display if analysis fails
            timestamp = f"[{segment.timestamp:.1f}s]"
            print(f"{timestamp} {segment.speaker}: {segment.text}")
            logger.error(f"Error in AI-first analysis: {e}")

    def _background_ai_analysis_sync_primary(self, segment: TranscriptSegment, segment_key: str, timestamp: str):
        """Primary AI analysis that updates the display with final result"""
        try:
            # Build context for AI analysis
            context_lines = []
            for seg in self.off_topic_analyzer.transcript_history:
                context_lines.append(f"[{seg.timestamp:.1f}s] {seg.speaker}: {seg.text}")

            full_context = "\n".join(context_lines) if context_lines else "Meeting start"
            new_phrase = f"[{segment.timestamp:.1f}s] {segment.speaker}: {segment.text}"

            # Prepare prompt
            try:
                prompt_template = self.off_topic_analyzer.config_loader.get_prompt()
            except Exception as e:
                logger.warning(f"Failed to load prompt from config: {e}, using fallback")
                prompt_template = """
You are a strict Scrum Master analyzing a daily standup. Flag technical deep-dives as OFF-TOPIC.

MEETING CONTEXT:
{context}

NEW STATEMENT:
{new_phrase}

Answer ONLY:
- "OFF_TOPIC: [reason]" for violations
- "ON_TOPIC" for proper standup updates
"""

            prompt = prompt_template.format(
                context=full_context,
                new_phrase=new_phrase
            )

            # Generate AI analysis
            response = self.off_topic_analyzer.model.generate_content(prompt)

            if response.text:
                ai_result = response.text.strip()
                self.off_topic_analyzer.ai_analysis_cache[segment_key] = ai_result

                # Update display with AI result
                if ai_result.startswith("OFF_TOPIC"):
                    self.off_topic_count += 1
                    reason = ai_result.replace("OFF_TOPIC:", "").strip()
                    print(f"{timestamp} 🤖 AI RESULT: 🚨 OFF-TOPIC")
                    print(f"         ⚠️  {reason}")
                else:
                    print(f"{timestamp} 🤖 AI RESULT: ✅ ON-TOPIC")
            else:
                print(f"{timestamp} 🤖 AI RESULT: ✅ ON-TOPIC (no response)")

            self.off_topic_analyzer.pending_ai_analysis.pop(segment_key, None)

        except Exception as e:
            logger.error(f"Error in primary AI analysis: {e}")
            # Fallback to local analysis
            local_result = self.off_topic_analyzer.analyze_new_phrase_sync(segment)

            if local_result.startswith("OFF_TOPIC"):
                self.off_topic_count += 1
                reason = local_result.replace("OFF_TOPIC:", "").strip()
                print(f"{timestamp} 🔄 AI FAILED - FALLBACK: 🚨 OFF-TOPIC")
                print(f"         ⚠️  {reason}")
            else:
                print(f"{timestamp} 🔄 AI FAILED - FALLBACK: ✅ ON-TOPIC")

            self.off_topic_analyzer.pending_ai_analysis.pop(segment_key, None)

    def _background_ai_analysis_sync(self, segment: TranscriptSegment, segment_key: str, quick_result: str):
        """Background AI analysis in separate thread"""
        try:
            # Build context for AI analysis
            context_lines = []
            for seg in self.off_topic_analyzer.transcript_history:
                context_lines.append(f"[{seg.timestamp:.1f}s] {seg.speaker}: {seg.text}")

            full_context = "\n".join(context_lines) if context_lines else "Meeting start"
            new_phrase = f"[{segment.timestamp:.1f}s] {segment.speaker}: {segment.text}"

            # Prepare prompt
            try:
                prompt_template = self.off_topic_analyzer.config_loader.get_prompt()
            except Exception as e:
                logger.warning(f"Failed to load prompt from config: {e}, using fallback")
                prompt_template = """
You are a strict Scrum Master analyzing a daily standup. Flag technical deep-dives as OFF-TOPIC.

MEETING CONTEXT:
{context}

NEW STATEMENT:
{new_phrase}

Answer ONLY:
- "OFF_TOPIC: [reason]" for violations
- "ON_TOPIC" for proper standup updates
"""

            prompt = prompt_template.format(
                context=full_context,
                new_phrase=new_phrase
            )

            # Generate AI analysis
            response = self.off_topic_analyzer.model.generate_content(prompt)

            if response.text:
                ai_result = response.text.strip()
                self.off_topic_analyzer.ai_analysis_cache[segment_key] = ai_result

                # Check if AI result differs from quick result
                if ai_result != quick_result:
                    timestamp = f"[{segment.timestamp:.1f}s]"

                    if ai_result.startswith("OFF_TOPIC") and not quick_result.startswith("OFF_TOPIC"):
                        # AI found off-topic that quick analysis missed
                        reason = ai_result.replace("OFF_TOPIC:", "").strip()
                        print(f"{timestamp} 🔄 AI UPDATE: {segment.speaker}: {segment.text}")
                        print(f"         🤖 AI DETECTED OFF-TOPIC: {reason}")
                        self.off_topic_count += 1

                    elif not ai_result.startswith("OFF_TOPIC") and quick_result.startswith("OFF_TOPIC"):
                        # AI corrected false positive from quick analysis
                        print(f"{timestamp} 🔄 AI CORRECTION: {segment.speaker}: {segment.text}")
                        print(f"         ✅ AI CONFIRMED ON-TOPIC (was flagged by quick analysis)")
                        self.off_topic_count = max(0, self.off_topic_count - 1)

            self.off_topic_analyzer.pending_ai_analysis.pop(segment_key, None)

        except Exception as e:
            logger.error(f"Error in background AI analysis: {e}")
            self.off_topic_analyzer.pending_ai_analysis.pop(segment_key, None)

    async def _analyze_and_display_segment(self, segment: TranscriptSegment):
        """Analyze segment and display with off-topic status (async version)"""
        try:
            # Add to analyzer history
            self.off_topic_analyzer.add_transcript_segment(segment)

            # Perform real-time analysis
            analysis_result = await self.off_topic_analyzer.analyze_new_phrase(segment)

            # Format output based on analysis
            timestamp = f"[{segment.timestamp:.1f}s]"

            if analysis_result.startswith("OFF_TOPIC"):
                self.off_topic_count += 1
                # Extract reason from analysis
                reason = analysis_result.replace("OFF_TOPIC:", "").strip()
                print(f"{timestamp} 🚨 {segment.speaker}: {segment.text}")
                print(f"         ⚠️  OFF-TOPIC: {reason}")
            else:
                print(f"{timestamp} ✅ {segment.speaker}: {segment.text}")

        except Exception as e:
            # Fallback to normal display if analysis fails
            timestamp = f"[{segment.timestamp:.1f}s]"
            print(f"{timestamp} {segment.speaker}: {segment.text}")
            logger.error(f"Error in analysis: {e}")
    
    def on_close(self, *args, **kwargs):
        """Handle connection close with analysis summary"""
        duration = time.time() - self.start_time if self.start_time else 0
        print("\n" + "=" * 65)
        print("🎙️  Live transcription with real-time analysis ended")
        print("=" * 65)
        print(f"📊 Transcription Stats:")
        print(f"   • Final transcripts: {self.final_count}")
        print(f"   • Duration: {duration:.1f}s")
        print(f"🤖 Analysis Stats:")
        print(f"   • Off-topic segments: {self.off_topic_count}")
        if self.final_count > 0:
            off_topic_ratio = (self.off_topic_count / self.final_count) * 100
            print(f"   • Off-topic ratio: {off_topic_ratio:.1f}%")

            # Meeting quality assessment
            if off_topic_ratio < 10:
                print("🎯 Meeting Quality: Excellent focus!")
            elif off_topic_ratio < 20:
                print("🎯 Meeting Quality: Good discipline")
            else:
                print("🎯 Meeting Quality: Needs improvement")

        if self.final_count == 0:
            print("⚠️  No transcripts received - possible API limit or connection issue")

        print("=" * 65)

    def on_error(self, error, **kwargs):
        """Handle connection errors"""
        print(f"❌ Error: {error}")
    
    async def stream_audio_file(self):
        """Stream audio file to Deepgram"""
        try:
            from realtime_audio_streamer import RealTimeAudioStreamer

            # Create audio streamer with configuration
            chunk_duration = self.config_loader.get_setting('audio.streaming.chunk_duration_ms', 500)
            speed_multiplier = self.config_loader.get_setting('audio.streaming.speed_multiplier', 0.8)

            streamer = RealTimeAudioStreamer(
                chunk_duration_ms=chunk_duration,
                speed_multiplier=speed_multiplier
            )

            # Load audio file
            if not streamer.load_audio_file(self.audio_file):
                print("❌ Failed to load audio file")
                return False

            # Track streaming progress
            self.chunks_sent = 0
            self.last_chunk_time = 0

            # Setup callback to send audio to Deepgram
            def on_audio_chunk(chunk_bytes: bytes, start_time: float, end_time: float):
                if self.dg_connection:
                    try:
                        self.dg_connection.send(chunk_bytes)
                        self.chunks_sent += 1
                        self.last_chunk_time = end_time
                        self.last_progress = end_time
                    except Exception:
                        pass

            streamer.on_audio_chunk = on_audio_chunk

            # Start streaming
            await streamer.start_streaming()

            # Send final flush to ensure all data is processed
            if self.dg_connection:
                try:
                    self.dg_connection.send(b'')
                except Exception:
                    pass

            return True

        except Exception as e:
            print(f"❌ Streaming error: {e}")
            return False
    
    def cleanup(self):
        """Cleanup connections"""
        if self.dg_connection:
            self.dg_connection.finish()
            self.dg_connection = None

async def main():
    """Main test function with real-time off-topic analysis"""
    print("🚀 Daily Catalyst - Live Transcription with Real-Time Off-Topic Analysis")
    print("=" * 75)

    # Get Deepgram API key
    deepgram_api_key = os.getenv('DEEPGRAM_API_KEY')
    if not deepgram_api_key:
        # Try to read from .env file directly
        env_file = Path('.env')
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    if line.startswith('DEEPGRAM_API_KEY='):
                        deepgram_api_key = line.split('=', 1)[1].strip()
                        break

    if not deepgram_api_key:
        print("❌ DEEPGRAM_API_KEY not found")
        return False

    # Get Gemini API key
    gemini_api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
    if not gemini_api_key:
        # Try to read from .env file directly
        env_file = Path('.env')
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    if line.startswith('GEMINI_API_KEY='):
                        gemini_api_key = line.split('=', 1)[1].strip()
                        break

    if not gemini_api_key:
        print("❌ GEMINI_API_KEY not found")
        print("💡 Real-time off-topic analysis requires Gemini API key")
        print("   Get free key at: https://makersuite.google.com/app/apikey")
        return False

    # Check audio file
    audio_file = "test_audio/meeting_example.wav"
    if not Path(audio_file).exists():
        print(f"❌ Audio file not found: {audio_file}")
        return False

    print(f"✅ Deepgram API key: Found")
    print(f"✅ Gemini API key: Found")
    print(f"✅ Audio file: {audio_file}")
    print()

    # Create test instance
    test = DeepgramOfficialTest(deepgram_api_key, gemini_api_key, audio_file)

    try:
        # Setup connection
        if not test.setup_connection():
            return False

        # Wait for connection to establish
        await asyncio.sleep(2)

        # Stream audio
        success = await test.stream_audio_file()

        # Wait for final transcripts
        await asyncio.sleep(20)

        # Check if we're still receiving transcripts
        initial_count = test.final_count
        await asyncio.sleep(5)
        if test.final_count > initial_count:
            await asyncio.sleep(10)

        return success and test.transcript_count > 0

    except KeyboardInterrupt:
        print("\n⏹️  Stopped by user")
        return True

    except Exception:
        return False

    finally:
        # Cleanup
        test.cleanup()

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
