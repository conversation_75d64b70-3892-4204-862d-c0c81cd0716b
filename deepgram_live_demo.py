#!/usr/bin/env python3
"""
Test script using official Deepgram SDK
Based on flask-live-transcription starter
"""

import asyncio
import logging
import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv
from typing import List, Dict, Optional
from dataclasses import dataclass

# Load environment variables
load_dotenv()

# Install deepgram-sdk if not available
try:
    from deepgram import (
        DeepgramClient,
        LiveTranscriptionEvents,
        LiveOptions,
        DeepgramClientOptions
    )
except ImportError:
    print("❌ Deepgram SDK not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "deepgram-sdk"])
    from deepgram import (
        DeepgramClient,
        LiveTranscriptionEvents,
        LiveOptions,
        DeepgramClientOptions
    )

# Install google-generativeai if not available
try:
    import google.generativeai as genai
except ImportError:
    print("❌ Google Generative AI not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "google-generativeai"])
    import google.generativeai as genai

# Add src to path for our audio streamer
sys.path.append('src')

# Configure logging - disable Deepgram SDK logs
logging.basicConfig(level=logging.WARNING)
logging.getLogger("deepgram").setLevel(logging.CRITICAL)
logging.getLogger("realtime_audio_streamer").setLevel(logging.CRITICAL)
logger = logging.getLogger(__name__)


@dataclass
class TranscriptSegment:
    """Represents a single transcript segment with metadata"""
    timestamp: float
    speaker: str
    text: str
    is_final: bool = False
    off_topic_analysis: Optional[str] = None


class RealTimeOffTopicAnalyzer:
    """Real-time off-topic analyzer using Gemini API"""

    # Professional prompt for real-time analysis
    REALTIME_PROMPT = """
AI Role: You are a strict yet fair leader (like Vito Corleone, or an experienced Scrum Master/Team Lead), who values efficiency, directness, and concrete results. Your task is to monitor daily stand-up meetings and identify instances where participants deviate from the meeting's core purpose.

Stand-up Purpose (Max 15 Minutes):
The daily stand-up is designed for a quick, focused exchange of information, addressing three key questions:
1. What did you do yesterday? (Brief, focused on completed or significantly progressed tasks)
2. What will you do today? (Clear plans for the current day, relevant to current sprint/task)
3. Are there any impediments or blockers? (Explicitly state what is hindering progress)

MEETING CONTEXT (previous statements):
{context}

NEW STATEMENT TO ANALYZE:
{new_phrase}

Analyze ONLY the new statement in context of the entire meeting.

CRITERIA FOR "OFF-TOPIC" (respond with "OFF_TOPIC: [reason]"):

1. EXCESSIVE TECHNICAL DETAIL (Most Critical):
   - Deep dives into implementation specifics (e.g., "The flex box was inside another flex box which had a width of 0 because of a missing prop")
   - Code snippets, stack traces, SQL queries
   - Detailed debugging explanations
   - Framework/library debates

2. PROBLEM-SOLVING ATTEMPTS:
   - "How should I fix this?", "What do you think about this approach?"
   - Initiating debates about solutions during standup

3. META-DISCUSSIONS:
   - Process improvement discussions
   - "We need to improve our meeting efficiency"
   - Lengthy contemplations without concrete outcomes

4. LENGTHY EXCUSES/EXPLANATIONS:
   - Detailed reasons for delays beyond brief "blocked" statements
   - Long explanations of circumstances

5. IRRELEVANT INFORMATION:
   - Non-work related conversations
   - Tasks from previous sprints not relevant to current work

CRITERIA FOR "ON-TOPIC" (respond with "ON_TOPIC"):
- Concrete outcomes: "Completed X module", "Pushed Y to testing"
- Clear actionable plans: "Today working on Z bug"
- Identified blockers: "Blocked awaiting response from Team C"
- Brief and to the point updates

Response format:
- If off-topic: "OFF_TOPIC: [specific reason]"
- If on-topic: "ON_TOPIC"
"""

    def __init__(self, gemini_api_key: str):
        """Initialize the real-time analyzer"""
        self.api_key = gemini_api_key
        self.transcript_history: List[TranscriptSegment] = []
        self.analysis_cache: Dict[str, str] = {}

        # Configure Gemini
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash-exp')

        print("🤖 Real-time Off-Topic Analyzer initialized")

    def add_transcript_segment(self, segment: TranscriptSegment):
        """Add new transcript segment to history"""
        self.transcript_history.append(segment)

        # Keep ALL segments from the beginning of the meeting
        # No truncation - we need full context for accurate analysis

    def analyze_new_phrase_sync(self, new_segment: TranscriptSegment) -> str:
        """Analyze new phrase in context of ENTIRE meeting history (synchronous version)"""
        try:
            # Build FULL context from ALL previous segments (from meeting start)
            context_lines = []
            for seg in self.transcript_history:  # ALL segments, not just last 10
                context_lines.append(f"[{seg.timestamp:.1f}s] {seg.speaker}: {seg.text}")

            full_context = "\n".join(context_lines) if context_lines else "Meeting start"

            # Format new phrase
            new_phrase = f"[{new_segment.timestamp:.1f}s] {new_segment.speaker}: {new_segment.text}"

            # Check cache first (but include full context length in cache key)
            cache_key = f"{len(self.transcript_history)}_{new_segment.text[:50]}_{len(full_context)}"
            if cache_key in self.analysis_cache:
                return self.analysis_cache[cache_key]

            # Enhanced analysis using full meeting context
            text_lower = new_segment.text.lower()
            text_length = len(new_segment.text)

            # Analyze meeting context to understand current phase
            meeting_duration = len(self.transcript_history)
            is_early_meeting = meeting_duration <= 3  # First few statements

            # Check if this is a continuation of previous off-topic discussion
            recent_context = ""
            if len(self.transcript_history) >= 2:
                recent_context = " ".join([seg.text.lower() for seg in self.transcript_history[-2:]])

            # 1. EXCESSIVE TECHNICAL DETAIL indicators (enhanced with context)
            excessive_technical = [
                'flexbox', 'css property', 'stack trace', 'error message', 'debugging',
                'variable value', 'sql query', 'code snippet', 'implementation detail',
                'missing prop', 'width of 0', 'justify-content', 'flex-direction',
                'devtools', 'computed styles', 'scss file', 'class declaration',
                'authentication system', 'jwt tokens', 'validation middleware',
                'backup strategy', 'disaster recovery', 'cross-region replication',
                'because of a missing', 'had a width of', 'property wasn\'t', 'css rule',
                'inside another flexbox', 'parent container', 'display: flex'
            ]

            # 2. PROBLEM-SOLVING ATTEMPTS indicators
            problem_solving = [
                'how should i', 'what do you think', 'should we use', 'perhaps we',
                'what about', 'how about', 'maybe we should', 'i think we need',
                'how should we fix', 'what\'s the best way', 'should we implement'
            ]

            # 3. META-DISCUSSION indicators
            meta_discussion = [
                'meeting efficiency', 'improve our process', 'we spend too much time',
                'we need to improve', 'our workflow', 'retrospective', 'process improvement',
                'css architecture', 'design system', 'different approach altogether'
            ]

            # 4. LENGTHY EXCUSES indicators
            lengthy_excuses = [
                'i spent the entire day', 'i had to do', 'i couldn\'t finish because',
                'the reason was', 'it took so long because', 'i was stuck on',
                'what should have been a simple', 'i had to work around'
            ]

            # Count indicators with context awareness
            technical_count = sum(1 for phrase in excessive_technical if phrase in text_lower)
            solving_count = sum(1 for phrase in problem_solving if phrase in text_lower)
            meta_count = sum(1 for phrase in meta_discussion if phrase in text_lower)
            excuse_count = sum(1 for phrase in lengthy_excuses if phrase in text_lower)

            # Context-aware analysis
            # If previous segments were technical, lower threshold for current segment
            context_technical = sum(1 for phrase in excessive_technical if phrase in recent_context)

            # More strict analysis - especially for technical details
            # Check for specific technical phrases that are clearly off-topic
            specific_technical_phrases = [
                'width of 0', 'missing prop', 'flexbox was inside', 'css rule',
                'justify-content', 'flex-direction', 'display: flex', 'parent container'
            ]

            specific_technical_count = sum(1 for phrase in specific_technical_phrases if phrase in text_lower)

            # Determine if off-topic based on content, length, context, and meeting phase
            if specific_technical_count >= 1:  # Any specific technical detail is off-topic
                return "OFF_TOPIC: Excessive technical detail"
            elif technical_count >= 2 or (technical_count >= 1 and text_length > 80):
                return "OFF_TOPIC: Excessive technical detail"
            elif technical_count >= 1 and context_technical >= 1 and text_length > 40:
                return "OFF_TOPIC: Continuing technical deep-dive"
            elif solving_count >= 1 and not is_early_meeting:
                return "OFF_TOPIC: Problem-solving attempt during standup"
            elif meta_count >= 1:
                return "OFF_TOPIC: Meta-discussion about process"
            elif excuse_count >= 1 and text_length > 120:
                return "OFF_TOPIC: Lengthy excuse/explanation"
            elif text_length > 200 and (technical_count >= 1 or solving_count >= 1):
                return "OFF_TOPIC: Too verbose for standup format"
            elif text_length > 120 and meeting_duration > 15:  # Later in meeting, be stricter
                return "OFF_TOPIC: Meeting running long, need brevity"

            # Cache result
            self.analysis_cache[cache_key] = "ON_TOPIC"
            return "ON_TOPIC"

        except Exception as e:
            logger.error(f"Error in real-time analysis: {e}")
            return "ON_TOPIC"  # Default to on-topic if analysis fails

    async def analyze_new_phrase(self, new_segment: TranscriptSegment) -> str:
        """Analyze new phrase in context of ENTIRE meeting history (async version)"""
        try:
            # Build FULL context from ALL previous segments (from meeting start)
            context_lines = []
            for seg in self.transcript_history:  # ALL segments, not just last 10
                context_lines.append(f"[{seg.timestamp:.1f}s] {seg.speaker}: {seg.text}")

            full_context = "\n".join(context_lines) if context_lines else "Meeting start"

            # Format new phrase
            new_phrase = f"[{new_segment.timestamp:.1f}s] {new_segment.speaker}: {new_segment.text}"

            # Check cache first (include full context in cache key)
            cache_key = f"{len(self.transcript_history)}_{new_segment.text[:50]}_{len(full_context)}"
            if cache_key in self.analysis_cache:
                return self.analysis_cache[cache_key]

            # Prepare prompt with FULL meeting context
            prompt = self.REALTIME_PROMPT.format(
                context=full_context,
                new_phrase=new_phrase
            )

            # Generate analysis
            response = self.model.generate_content(prompt)

            if response.text:
                result = response.text.strip()
                self.analysis_cache[cache_key] = result
                return result
            else:
                return "ON_TOPIC"

        except Exception as e:
            logger.error(f"Error in real-time analysis: {e}")
            return "ON_TOPIC"  # Default to on-topic if analysis fails

class DeepgramOfficialTest:
    """Test class using official Deepgram SDK with real-time off-topic analysis"""

    def __init__(self, deepgram_api_key: str, gemini_api_key: str, audio_file: str):
        self.deepgram_api_key = deepgram_api_key
        self.gemini_api_key = gemini_api_key
        self.audio_file = audio_file

        # Statistics
        self.transcript_count = 0
        self.final_count = 0
        self.interim_count = 0
        self.off_topic_count = 0
        self.start_time = None
        self.chunks_sent = 0
        self.last_chunk_time = 0
        self.last_progress = 0

        # Deepgram client
        config = DeepgramClientOptions(
            verbose=logging.CRITICAL,  # Disable verbose logging
            options={"keepalive": "true"}
        )
        self.deepgram = DeepgramClient(deepgram_api_key, config)
        self.dg_connection = None

        # Real-time off-topic analyzer
        self.off_topic_analyzer = RealTimeOffTopicAnalyzer(gemini_api_key)

        print("🎙️  Live Transcription with Real-Time Off-Topic Analysis")
        print("=" * 65)
        print("🤖 Powered by Deepgram + Google Gemini Flash 2.5")
        print("=" * 65)
    
    def setup_connection(self):
        """Setup Deepgram connection with callbacks"""
        # Initialize connection (use websocket instead of live)
        self.dg_connection = self.deepgram.listen.websocket.v("1")

        # Setup event handlers with lambda to handle SDK callback signature
        self.dg_connection.on(LiveTranscriptionEvents.Open, lambda self_ref, *args, **kwargs: self.on_open(*args, **kwargs))
        self.dg_connection.on(LiveTranscriptionEvents.Transcript, lambda self_ref, result, **kwargs: self.on_message(result, **kwargs))
        self.dg_connection.on(LiveTranscriptionEvents.Close, lambda self_ref, *args, **kwargs: self.on_close(*args, **kwargs))
        self.dg_connection.on(LiveTranscriptionEvents.Error, lambda self_ref, error, **kwargs: self.on_error(error, **kwargs))

        # Define options with enhanced speaker diarization
        options = LiveOptions(
            model="nova-3",
            language="en-US",
            smart_format=True,
            diarize=True,
            interim_results=False,  # Disable interim for better final results
            punctuate=True,
            encoding="linear16",
            sample_rate=16000,
            channels=1,
            # Additional parameters for better diarization
            filler_words=False,
            numerals=True
        )

        # Start connection
        if self.dg_connection.start(options) is False:
            return False

        return True
    
    def on_open(self, *args, **kwargs):
        """Handle connection open"""
        self.start_time = time.time()
        print("🎙️  Live transcription started...")
        print("=" * 50)

    def on_message(self, result, **kwargs):
        """Handle transcription results with real-time off-topic analysis"""
        if not result:
            return

        try:
            # Get transcript
            transcript = None
            if hasattr(result, 'channel') and hasattr(result.channel, 'alternatives'):
                if result.channel.alternatives and len(result.channel.alternatives) > 0:
                    transcript = result.channel.alternatives[0].transcript

            if transcript and len(transcript.strip()) > 0:
                self.transcript_count += 1

                # Determine result type
                is_final = getattr(result, 'is_final', False)
                speech_final = getattr(result, 'speech_final', False)

                # Only process final results for analysis
                if speech_final or (is_final and len(transcript.strip()) > 3):
                    self.final_count += 1

                    # Get speaker information with enhanced detection
                    speakers = set()
                    words = getattr(result.channel.alternatives[0], 'words', [])

                    # Collect all speakers from words
                    for word in words:
                        if hasattr(word, 'speaker') and word.speaker is not None:
                            speakers.add(word.speaker)

                    # If no speaker info in words, try alternative methods
                    if not speakers:
                        # Check if there's speaker info at alternative level
                        alt = result.channel.alternatives[0]
                        if hasattr(alt, 'speaker') and alt.speaker is not None:
                            speakers.add(alt.speaker)

                        # Check metadata for speaker info
                        if hasattr(result, 'metadata') and hasattr(result.metadata, 'speaker'):
                            speakers.add(result.metadata.speaker)

                    # Format speaker info
                    if speakers:
                        speaker_list = sorted(speakers)
                        if len(speaker_list) == 1:
                            speaker_info = f"Speaker {speaker_list[0]}"
                        else:
                            speaker_info = f"Speakers {', '.join(map(str, speaker_list))}"
                    else:
                        # Fallback: try to detect speaker change based on timing
                        speaker_info = "Unknown Speaker"

                    # Get timing info - try multiple sources
                    start_time = 0

                    # Method 1: From result metadata
                    if hasattr(result, 'metadata') and hasattr(result.metadata, 'request_id'):
                        start_time = getattr(result.metadata, 'start', 0)

                    # Method 2: From channel alternatives
                    if start_time == 0 and hasattr(result, 'channel') and hasattr(result.channel, 'alternatives'):
                        if result.channel.alternatives:
                            start_time = getattr(result.channel.alternatives[0], 'start', 0)

                    # Method 3: From words timing
                    if start_time == 0 and words:
                        first_word = words[0]
                        if hasattr(first_word, 'start'):
                            start_time = first_word.start

                    # Method 4: Calculate from stream time
                    if start_time == 0 and self.start_time:
                        start_time = time.time() - self.start_time

                    # Create transcript segment
                    segment = TranscriptSegment(
                        timestamp=start_time,
                        speaker=speaker_info,
                        text=transcript,
                        is_final=True
                    )

                    # Perform real-time off-topic analysis (synchronous version)
                    self._analyze_and_display_segment_sync(segment)
                else:
                    self.interim_count += 1

        except Exception as e:
            # Silent error handling
            logger.error(f"Error in on_message: {e}")

    def _analyze_and_display_segment_sync(self, segment: TranscriptSegment):
        """Analyze segment and display with off-topic status (synchronous version)"""
        try:
            # Add to analyzer history
            self.off_topic_analyzer.add_transcript_segment(segment)

            # Perform real-time analysis (synchronous)
            analysis_result = self.off_topic_analyzer.analyze_new_phrase_sync(segment)

            # Format output based on analysis
            timestamp = f"[{segment.timestamp:.1f}s]"

            if analysis_result.startswith("OFF_TOPIC"):
                self.off_topic_count += 1
                # Extract reason from analysis
                reason = analysis_result.replace("OFF_TOPIC:", "").strip()
                print(f"{timestamp} 🚨 {segment.speaker}: {segment.text}")
                print(f"         ⚠️  OFF-TOPIC: {reason}")
            else:
                print(f"{timestamp} ✅ {segment.speaker}: {segment.text}")

        except Exception as e:
            # Fallback to normal display if analysis fails
            timestamp = f"[{segment.timestamp:.1f}s]"
            print(f"{timestamp} {segment.speaker}: {segment.text}")
            logger.error(f"Error in analysis: {e}")

    async def _analyze_and_display_segment(self, segment: TranscriptSegment):
        """Analyze segment and display with off-topic status (async version)"""
        try:
            # Add to analyzer history
            self.off_topic_analyzer.add_transcript_segment(segment)

            # Perform real-time analysis
            analysis_result = await self.off_topic_analyzer.analyze_new_phrase(segment)

            # Format output based on analysis
            timestamp = f"[{segment.timestamp:.1f}s]"

            if analysis_result.startswith("OFF_TOPIC"):
                self.off_topic_count += 1
                # Extract reason from analysis
                reason = analysis_result.replace("OFF_TOPIC:", "").strip()
                print(f"{timestamp} 🚨 {segment.speaker}: {segment.text}")
                print(f"         ⚠️  OFF-TOPIC: {reason}")
            else:
                print(f"{timestamp} ✅ {segment.speaker}: {segment.text}")

        except Exception as e:
            # Fallback to normal display if analysis fails
            timestamp = f"[{segment.timestamp:.1f}s]"
            print(f"{timestamp} {segment.speaker}: {segment.text}")
            logger.error(f"Error in analysis: {e}")
    
    def on_close(self, *args, **kwargs):
        """Handle connection close with analysis summary"""
        duration = time.time() - self.start_time if self.start_time else 0
        print("\n" + "=" * 65)
        print("🎙️  Live transcription with real-time analysis ended")
        print("=" * 65)
        print(f"📊 Transcription Stats:")
        print(f"   • Final transcripts: {self.final_count}")
        print(f"   • Duration: {duration:.1f}s")
        print(f"🤖 Analysis Stats:")
        print(f"   • Off-topic segments: {self.off_topic_count}")
        if self.final_count > 0:
            off_topic_ratio = (self.off_topic_count / self.final_count) * 100
            print(f"   • Off-topic ratio: {off_topic_ratio:.1f}%")

            # Meeting quality assessment
            if off_topic_ratio < 10:
                print("🎯 Meeting Quality: Excellent focus!")
            elif off_topic_ratio < 20:
                print("🎯 Meeting Quality: Good discipline")
            else:
                print("🎯 Meeting Quality: Needs improvement")

        if self.final_count == 0:
            print("⚠️  No transcripts received - possible API limit or connection issue")

        print("=" * 65)

    def on_error(self, error, **kwargs):
        """Handle connection errors"""
        print(f"❌ Error: {error}")
    
    async def stream_audio_file(self):
        """Stream audio file to Deepgram"""
        try:
            from realtime_audio_streamer import RealTimeAudioStreamer

            # Create audio streamer optimized for speaker diarization
            streamer = RealTimeAudioStreamer(
                chunk_duration_ms=500,  # Even larger chunks for better speaker analysis
                speed_multiplier=0.8    # Slower than real-time for better processing
            )

            # Load audio file
            if not streamer.load_audio_file(self.audio_file):
                print("❌ Failed to load audio file")
                return False

            # Track streaming progress
            self.chunks_sent = 0
            self.last_chunk_time = 0

            # Setup callback to send audio to Deepgram
            def on_audio_chunk(chunk_bytes: bytes, start_time: float, end_time: float):
                if self.dg_connection:
                    try:
                        self.dg_connection.send(chunk_bytes)
                        self.chunks_sent += 1
                        self.last_chunk_time = end_time
                        self.last_progress = end_time
                    except Exception:
                        pass

            streamer.on_audio_chunk = on_audio_chunk

            # Start streaming
            await streamer.start_streaming()

            # Send final flush to ensure all data is processed
            if self.dg_connection:
                try:
                    self.dg_connection.send(b'')
                except Exception:
                    pass

            return True

        except Exception as e:
            print(f"❌ Streaming error: {e}")
            return False
    
    def cleanup(self):
        """Cleanup connections"""
        if self.dg_connection:
            self.dg_connection.finish()
            self.dg_connection = None

async def main():
    """Main test function with real-time off-topic analysis"""
    print("🚀 Daily Catalyst - Live Transcription with Real-Time Off-Topic Analysis")
    print("=" * 75)

    # Get Deepgram API key
    deepgram_api_key = os.getenv('DEEPGRAM_API_KEY')
    if not deepgram_api_key:
        # Try to read from .env file directly
        env_file = Path('.env')
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    if line.startswith('DEEPGRAM_API_KEY='):
                        deepgram_api_key = line.split('=', 1)[1].strip()
                        break

    if not deepgram_api_key:
        print("❌ DEEPGRAM_API_KEY not found")
        return False

    # Get Gemini API key
    gemini_api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
    if not gemini_api_key:
        # Try to read from .env file directly
        env_file = Path('.env')
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    if line.startswith('GEMINI_API_KEY='):
                        gemini_api_key = line.split('=', 1)[1].strip()
                        break

    if not gemini_api_key:
        print("❌ GEMINI_API_KEY not found")
        print("💡 Real-time off-topic analysis requires Gemini API key")
        print("   Get free key at: https://makersuite.google.com/app/apikey")
        return False

    # Check audio file
    audio_file = "test_audio/meeting_example.wav"
    if not Path(audio_file).exists():
        print(f"❌ Audio file not found: {audio_file}")
        return False

    print(f"✅ Deepgram API key: Found")
    print(f"✅ Gemini API key: Found")
    print(f"✅ Audio file: {audio_file}")
    print()

    # Create test instance
    test = DeepgramOfficialTest(deepgram_api_key, gemini_api_key, audio_file)

    try:
        # Setup connection
        if not test.setup_connection():
            return False

        # Wait for connection to establish
        await asyncio.sleep(2)

        # Stream audio
        success = await test.stream_audio_file()

        # Wait for final transcripts
        await asyncio.sleep(20)

        # Check if we're still receiving transcripts
        initial_count = test.final_count
        await asyncio.sleep(5)
        if test.final_count > initial_count:
            await asyncio.sleep(10)

        return success and test.transcript_count > 0

    except KeyboardInterrupt:
        print("\n⏹️  Stopped by user")
        return True

    except Exception:
        return False

    finally:
        # Cleanup
        test.cleanup()

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
