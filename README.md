# Daily Catalyst

🎙️ **Real-time video transcription with AI-powered off-topic detection for meetings**

Transform your meeting videos into live transcripts with instant off-topic analysis. Perfect for daily standups, team meetings, and productivity monitoring.

## ⚡ Quick Start

### 1. Prerequisites

- Python 3.8+
- [Deepgram API key](https://deepgram.com) (for transcription)
- [Google Gemini API key](https://aistudio.google.com) (for off-topic analysis)

### 2. Installation

```bash
git clone <repository-url>
cd daily-catalyst
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 3. Set API Keys

```bash
export DEEPGRAM_API_KEY="your_deepgram_api_key_here"
export GEMINI_API_KEY="your_gemini_api_key_here"
```

### 4. Run Real-Time Analysis

```bash
python optimized_fast_video.py test_video/meeting_video.mp4
```

## 🎯 What You Get

**Live Transcription:**
```
[0.4s] ✅ Speaker 0: Good morning, my dear team.
[2.6s] ✅ Speaker 0: Let's start the stand up...
[7.2s] ✅ Speaker 0: What have you done yesterday?
[13.5s] ✅ Speaker 1: So yesterday, mostly meeting.
```

**Off-Topic Detection:**
```
[45.2s] 🚨 Speaker 1: Let me explain how CSS flexbox works...
         ⚠️  OFF_TOPIC: Excessive technical details not relevant to standup format
```

**Meeting Quality Analysis:**
```
📊 Final Statistics:
   • Total segments: 55
   • Off-topic segments: 3 (5.5%)
   • Meeting Quality: Excellent focus!
```

## 🚀 Features

- **⚡ Ultra-fast response** - 10ms chunks for minimal delay
- **🎬 Video playback** - Watch video while getting live transcription
- **🎙️ Speaker identification** - Automatic speaker diarization
- **🤖 AI analysis** - Real-time off-topic detection using Gemini Flash 2.5
- **📊 Quality metrics** - Meeting focus and efficiency analysis

## 🔧 API Keys Setup

### Deepgram (Transcription)
1. Go to [deepgram.com](https://deepgram.com)
2. Sign up and get your API key
3. Set: `export DEEPGRAM_API_KEY="your_key"`

### Google Gemini (AI Analysis)
1. Go to [aistudio.google.com](https://aistudio.google.com)
2. Get your API key
3. Set: `export GEMINI_API_KEY="your_key"`

## 📁 File Structure

```
daily-catalyst/
├── optimized_fast_video.py    # 🎯 Main script (use this!)
├── test_video/               # 📹 Sample video for testing
├── deepgram_live_demo.py     # 🎙️ Transcription engine
├── src/realtime_audio_streamer.py  # ⚡ Audio streaming
└── requirements.txt          # 📦 Dependencies
```

## 🎬 Usage Examples

**Basic usage:**
```bash
python optimized_fast_video.py your_meeting.mp4
```

**With test video:**
```bash
python optimized_fast_video.py test_video/meeting_video.mp4
```

**Check if everything works:**
```bash
source venv/bin/activate
python optimized_fast_video.py test_video/meeting_video.mp4
```

## 🎯 Perfect For

- **Daily standups** - Keep meetings focused and on-topic
- **Team meetings** - Real-time transcription and analysis
- **Meeting reviews** - Analyze meeting quality and efficiency
- **Productivity monitoring** - Track off-topic discussions

## 📊 Performance

- **Response time**: ~1-2 seconds delay
- **Chunk size**: 10ms (ultra-responsive)
- **Accuracy**: High-quality Deepgram Nova-2 model
- **Speed**: Real-time (1.0x video speed)

## 🆘 Troubleshooting

**No transcription appearing?**
- Check your Deepgram API key
- Ensure audio is audible in the video

**Off-topic analysis not working?**
- Verify your Gemini API key
- Check internet connection

**Video not playing?**
- Install ffplay: `sudo apt install ffmpeg`
- Check video file format (MP4 recommended)

## 📄 License

MIT License - see LICENSE file for details.
