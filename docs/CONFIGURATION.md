# Configuration System Documentation

Daily Catalyst features a flexible configuration system that allows you to customize AI prompts, technical parameters, and application behavior without modifying code.

## Overview

The configuration system consists of three main files:

- **`config/prompts.yaml`** - AI prompts for off-topic detection and analysis
- **`config/settings.yaml`** - Technical parameters (API settings, audio processing, analysis thresholds)
- **`config.yaml`** - Main application settings (existing file)

## Configuration Files

### 1. Prompts Configuration (`config/prompts.yaml`)

This file contains all AI prompts used for off-topic detection:

```yaml
off_topic_detection:
  realtime_prompt: |
    You are a strict Scrum Master analyzing a daily standup...

  alternative_prompts:
    strict_architect: |
      You are the "Architect of Efficiency"...
    lenient_facilitator: |
      You are a supportive meeting facilitator...

prompt_versions:
  current_version: "realtime_prompt"
  available_versions:
    - name: "realtime_prompt"
      effectiveness_score: 0.85
```

#### Key Sections:

- **`realtime_prompt`** - Main prompt used for analysis
- **`alternative_prompts`** - Different prompt variations for A/B testing
- **`prompt_versions`** - Version management and effectiveness tracking
- **`fallback_prompts`** - Keywords and phrases for local analysis when AI is unavailable

### 2. Settings Configuration (`config/settings.yaml`)

Technical parameters and API settings:

```yaml
api:
  deepgram:
    model: "nova-3"
    language: "en-US"
    smart_format: true
    diarize: true

  gemini:
    models:
      - name: "gemini-2.5-flash"
        rpm_limit: 8
        daily_limit: 200

audio:
  ffmpeg:
    input_format: "pulse"
    sample_rate: 16000
    channels: 1

  streaming:
    chunk_size: 2048
    chunk_duration_ms: 500

analysis:
  text_analysis:
    min_segment_length: 30
    suspicious_length_threshold: 120

  ai_analysis:
    use_ai_for_suspicious: true
    enable_caching: true
```

#### Key Sections:

- **`api`** - API configuration for Deepgram and Gemini
- **`audio`** - Audio processing and streaming settings
- **`analysis`** - Text analysis thresholds and AI analysis triggers
- **`performance`** - Timeouts, retries, and memory management
- **`display`** - Output formatting and status indicators

## Usage

### Loading Configuration

The configuration system is automatically loaded when you import the classes:

```python
from deepgram_live_demo import RealTimeOffTopicAnalyzer
from live_transcription import LiveTranscriber

# Configuration is loaded automatically
analyzer = RealTimeOffTopicAnalyzer(api_key)
transcriber = LiveTranscriber(deepgram_key, gemini_key)
```

### Manual Configuration Loading

You can also load configuration manually:

```python
from config.config_loader import load_config, get_prompt, get_setting

# Load all configurations
config = load_config()

# Get specific prompt
prompt = get_prompt("realtime_prompt")

# Get specific setting
chunk_size = get_setting("audio.streaming.chunk_size", 2048)
```

### Custom Configuration Loader

For advanced usage:

```python
from config.config_loader import ConfigLoader

# Create custom loader
loader = ConfigLoader("/path/to/config/directory")
config = loader.load_all_configs()

# Check for errors
if not config.is_valid:
    for error in config.errors:
        print(f"Error: {error}")
```

## Customization

### 1. Modifying Prompts

Edit `config/prompts.yaml` to change AI behavior:

```yaml
off_topic_detection:
  realtime_prompt: |
    Your custom prompt here.
    Use {context} and {new_phrase} placeholders.
```

### 2. A/B Testing Prompts

Switch between different prompts:

```yaml
prompt_versions:
  current_version: "strict_architect"  # Change this line
```

### 3. Adding Custom Prompts

Create files in `config/custom_prompts/`:

```bash
mkdir -p config/custom_prompts
cat > config/custom_prompts/my_prompts.yaml << EOF
my_custom_prompt: |
  Custom prompt for specific use case...
EOF
```

### 4. Adjusting Technical Settings

Modify `config/settings.yaml`:

```yaml
# Increase AI analysis sensitivity
analysis:
  text_analysis:
    min_segment_length: 20  # Lower threshold
    suspicious_length_threshold: 100

# Adjust audio quality
audio:
  ffmpeg:
    sample_rate: 22050  # Higher quality
  streaming:
    chunk_size: 4096  # Larger chunks
```

## Validation

The configuration system includes validation:

```python
from config.config_loader import load_config

config = load_config()

if not config.is_valid:
    print("Configuration errors:")
    for error in config.errors:
        print(f"  - {error}")

if config.warnings:
    print("Configuration warnings:")
    for warning in config.warnings:
        print(f"  - {warning}")
```

## Fallback Behavior

If configuration files are missing or invalid:

1. **Prompts** - Falls back to hardcoded default prompt
2. **Settings** - Uses sensible defaults for all parameters
3. **Errors** - Logged but don't prevent operation

## Best Practices

### 1. Backup Configuration

```bash
cp -r config/ config_backup/
```

### 2. Version Control

Add configuration to git but be careful with sensitive data:

```bash
git add config/prompts.yaml config/settings.yaml
# Don't commit API keys or sensitive settings
```

### 3. Environment-Specific Settings

Use different configurations for different environments:

```bash
# Development
cp config/settings.yaml config/settings.dev.yaml

# Production
cp config/settings.yaml config/settings.prod.yaml
```

### 4. Testing Configuration Changes

Test configuration changes before deployment:

```python
from config.config_loader import ConfigLoader

# Test new configuration
loader = ConfigLoader()
config = loader.load_all_configs()

if config.is_valid:
    print("✅ Configuration is valid")
else:
    print("❌ Configuration has errors")
    for error in config.errors:
        print(f"  - {error}")
```

## Troubleshooting

### Common Issues

1. **YAML Syntax Errors**
   ```
   Error: Invalid YAML in config/prompts.yaml: ...
   ```
   Solution: Check YAML syntax, especially indentation

2. **Missing Placeholders**
   ```
   Error: Missing placeholder {context} in realtime_prompt
   ```
   Solution: Ensure prompts contain required placeholders

3. **Invalid Settings**
   ```
   Error: Missing field 'name' in gemini model 0
   ```
   Solution: Check settings structure matches expected format

### Debug Mode

Enable debug logging to see configuration loading:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

from config.config_loader import load_config
config = load_config()
```

## Migration from Hardcoded Values

If you're upgrading from a version with hardcoded prompts/settings:

1. **Backup existing code**
2. **Run with default configuration** - should work without changes
3. **Gradually customize** - modify configuration files as needed
4. **Test thoroughly** - ensure behavior matches expectations

The system maintains backward compatibility, so existing code continues to work unchanged.